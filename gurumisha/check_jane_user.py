#!/usr/bin/env python3
import os
import sys
import django

# Setup Django environment
sys.path.append('/home/<USER>/Documents/augment-projects/gurumisha/gurumisha')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gurumisha_project.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import Vendor

User = get_user_model()

def check_jane_user():
    print("=== Checking Jane User Status ===")
    
    # Check if jane user exists
    try:
        jane = User.objects.get(username='jane')
        print(f"✅ Jane user found: {jane}")
        print(f"   - Role: {jane.role}")
        print(f"   - Active: {jane.is_active}")
        print(f"   - Staff: {jane.is_staff}")
        print(f"   - Email verified: {jane.is_email_verified}")
        
        # Check vendor profile
        try:
            vendor = jane.vendor
            print(f"✅ Jane vendor profile: {vendor}")
            print(f"   - Company: {vendor.company_name}")
            print(f"   - Approved: {vendor.is_approved}")
            print(f"   - Verification status: {vendor.verification_status}")
        except Vendor.DoesNotExist:
            print("❌ Jane does not have a vendor profile")
            
            # Create vendor profile for jane
            print("Creating vendor profile for jane...")
            vendor = Vendor.objects.create(
                user=jane,
                company_name=f"{jane.first_name} {jane.last_name}".strip() or jane.username,
                is_approved=True,  # Approve immediately for testing
                verification_status='verified'
            )
            print(f"✅ Created vendor profile: {vendor}")
            
    except User.DoesNotExist:
        print("❌ Jane user does not exist")
        print("Creating jane user...")
        
        # Create jane user
        jane = User.objects.create_user(
            username='jane',
            email='<EMAIL>',
            password='password123',
            first_name='Jane',
            last_name='Vendor',
            role='vendor',
            is_active=True,
            is_email_verified=True
        )
        print(f"✅ Created jane user: {jane}")
        
        # Create vendor profile
        vendor = Vendor.objects.create(
            user=jane,
            company_name='Jane Motors',
            is_approved=True,
            verification_status='verified'
        )
        print(f"✅ Created vendor profile: {vendor}")
    
    print("\n=== All Users ===")
    for user in User.objects.all():
        vendor_status = "No vendor profile"
        try:
            vendor = user.vendor
            vendor_status = f"Vendor: {vendor.company_name} (Approved: {vendor.is_approved})"
        except Vendor.DoesNotExist:
            pass
        
        print(f"- {user.username} ({user.role}) - Active: {user.is_active} - {vendor_status}")

if __name__ == '__main__':
    check_jane_user()
